# NDHM FHIR R4 Billing and Invoice Implementation Guide

## Table of Contents
1. [Billing Architecture Overview](#billing-architecture-overview)
2. [Invoice Profile Structure](#invoice-profile-structure)
3. [ChargeItem Profile](#chargeitem-profile)
4. [Price Components](#price-components)
5. [Indian Tax Implementation](#indian-tax-implementation)
6. [Implementation Examples](#implementation-examples)
7. [Best Practices](#best-practices)

## Billing Architecture Overview

The NDHM FHIR R4 billing system is designed to handle various healthcare billing scenarios in the Indian context, with proper GST implementation and transparency in pricing.

### Key Components
1. **Invoice**: Main billing document
2. **ChargeItem**: Individual billable items
3. **InvoiceRecord**: Composition-based billing artifact
4. **Price Components**: Detailed price breakdown

### Billing Flow
```
Service/Product → ChargeItem → Invoice → InvoiceRecord → Patient/Payer
```

## Invoice Profile Structure

### Mandatory Elements

#### Core Information
```json
{
  "resourceType": "Invoice",
  "identifier": [{"value": "INV-2024-001"}],
  "status": "issued",
  "type": {
    "coding": [{
      "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
      "code": "00",
      "display": "Consultation"
    }]
  },
  "subject": {"reference": "Patient/example-01"},
  "date": "2024-01-15T10:00:00+05:30"
}
```

#### Line Items Structure
```json
{
  "lineItem": [{
    "sequence": 1,
    "chargeItemReference": {"reference": "ChargeItem/consultation-001"},
    "priceComponent": [
      {
        "type": "base",
        "code": {"coding": [{"code": "01", "display": "Rate"}]},
        "amount": {"value": 500, "currency": "INR"}
      }
    ]
  }]
}
```

#### Totals
```json
{
  "totalNet": {"value": 590, "currency": "INR"},
  "totalGross": {"value": 500, "currency": "INR"}
}
```

### Invoice Types (NDHM Billing Codes)

| Code | Display | Use Case |
|------|---------|----------|
| 00 | Consultation | Doctor visit fees |
| 01 | Pharmacy | Medication purchases |
| 02 | Diagnostic | Lab tests, imaging |
| 03 | Procedure | Medical procedures |
| 04 | Hospitalization | Inpatient charges |

## ChargeItem Profile

### Purpose
ChargeItem resources contain detailed information about individual billable items, including medications, services, and procedures.

### Mandatory Elements
- **identifier**: Unique charge identifier
- **status**: Charge status (planned, billable, not-billable)
- **code**: Service/product code (SNOMED CT preferred)
- **subject**: Patient reference
- **quantity**: Amount/quantity charged
- **priceOverride**: Actual price charged

### Medication ChargeItem Example
```json
{
  "resourceType": "ChargeItem",
  "id": "medication-paracetamol-001",
  "status": "billable",
  "code": {
    "coding": [{
      "system": "http://snomed.info/sct",
      "code": "387517004",
      "display": "Paracetamol"
    }]
  },
  "subject": {"reference": "Patient/example-01"},
  "quantity": {"value": 10, "unit": "tablet"},
  "priceOverride": {"value": 80, "currency": "INR"}
}
```

### Consultation ChargeItem Example
```json
{
  "resourceType": "ChargeItem",
  "id": "consultation-001",
  "status": "billable",
  "code": {
    "coding": [{
      "system": "http://snomed.info/sct",
      "code": "11429006",
      "display": "Consultation"
    }]
  },
  "subject": {"reference": "Patient/example-01"},
  "quantity": {"value": 1, "unit": "visit"},
  "priceOverride": {"value": 550, "currency": "INR"}
}
```

## Price Components

### NDHM Price Component Codes

| Code | Display | Type | Description |
|------|---------|------|-------------|
| 00 | MRP | informational | Maximum Retail Price |
| 01 | Rate | base | Actual charged price |
| 02 | Discount | discount | Applied discount |
| 03 | CGST | tax | Central Goods and Services Tax |
| 04 | SGST | tax | State Goods and Services Tax |
| 05 | IGST | tax | Integrated Goods and Services Tax |
| 06 | CESS | tax | Additional cess |

### Price Component Types (FHIR Standard)
- **base**: Base price of the item
- **surcharge**: Additional charges
- **deduction**: Reductions in price
- **discount**: Discount applied
- **tax**: Tax components
- **informational**: Reference prices (like MRP)

### Implementation Pattern
```json
{
  "priceComponent": [
    {
      "type": "informational",
      "code": {
        "coding": [{
          "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
          "code": "00",
          "display": "MRP"
        }]
      },
      "amount": {"value": 600, "currency": "INR"}
    },
    {
      "type": "base",
      "code": {
        "coding": [{
          "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
          "code": "01",
          "display": "Rate"
        }]
      },
      "amount": {"value": 550, "currency": "INR"}
    },
    {
      "type": "discount",
      "code": {
        "coding": [{
          "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-price-components",
          "code": "02",
          "display": "Discount"
        }]
      },
      "amount": {"value": 50, "currency": "INR"}
    }
  ]
}
```

## Indian Tax Implementation

### GST Structure in India
- **CGST**: Central Goods and Services Tax (charged by central government)
- **SGST**: State Goods and Services Tax (charged by state government)
- **IGST**: Integrated GST (for inter-state transactions)

### Tax Rates by Category
| Category | CGST | SGST | Total GST |
|----------|------|------|-----------|
| Essential Medicines | 0% | 0% | 0% |
| Non-essential Medicines | 6% | 6% | 12% |
| Medical Devices | 9% | 9% | 18% |
| Consultation Services | 9% | 9% | 18% |

### Tax Implementation Example
```json
{
  "priceComponent": [
    {
      "type": "base",
      "code": {"coding": [{"code": "01", "display": "Rate"}]},
      "amount": {"value": 500, "currency": "INR"}
    },
    {
      "type": "tax",
      "code": {"coding": [{"code": "03", "display": "CGST"}]},
      "amount": {"value": 45, "currency": "INR"}
    },
    {
      "type": "tax",
      "code": {"coding": [{"code": "04", "display": "SGST"}]},
      "amount": {"value": 45, "currency": "INR"}
    }
  ]
}
```

### Tax Calculation Logic
```javascript
// Example tax calculation for 18% GST
const baseAmount = 500;
const gstRate = 0.18;
const cgstRate = gstRate / 2; // 9%
const sgstRate = gstRate / 2; // 9%

const cgstAmount = baseAmount * cgstRate; // 45
const sgstAmount = baseAmount * sgstRate; // 45
const totalAmount = baseAmount + cgstAmount + sgstAmount; // 590
```

## Implementation Examples

### Consultation Invoice
```json
{
  "resourceType": "Invoice",
  "id": "consultation-invoice-001",
  "identifier": [{"value": "CONS/2024/001"}],
  "status": "issued",
  "type": {
    "coding": [{
      "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
      "code": "00",
      "display": "Consultation"
    }]
  },
  "subject": {"reference": "Patient/example-01"},
  "date": "2024-01-15T10:00:00+05:30",
  "participant": [{
    "actor": {"reference": "Practitioner/doctor-001"}
  }],
  "lineItem": [{
    "sequence": 1,
    "chargeItemReference": {"reference": "ChargeItem/consultation-001"},
    "priceComponent": [
      {
        "type": "informational",
        "code": {"coding": [{"code": "00", "display": "MRP"}]},
        "amount": {"value": 600, "currency": "INR"}
      },
      {
        "type": "base",
        "code": {"coding": [{"code": "01", "display": "Rate"}]},
        "amount": {"value": 550, "currency": "INR"}
      },
      {
        "type": "discount",
        "code": {"coding": [{"code": "02", "display": "Discount"}]},
        "amount": {"value": 50, "currency": "INR"}
      },
      {
        "type": "tax",
        "code": {"coding": [{"code": "03", "display": "CGST"}]},
        "amount": {"value": 45, "currency": "INR"}
      },
      {
        "type": "tax",
        "code": {"coding": [{"code": "04", "display": "SGST"}]},
        "amount": {"value": 45, "currency": "INR"}
      }
    ]
  }],
  "totalNet": {"value": 590, "currency": "INR"},
  "totalGross": {"value": 500, "currency": "INR"}
}
```

### Pharmacy Invoice (Multiple Items)
```json
{
  "resourceType": "Invoice",
  "id": "pharmacy-invoice-001",
  "identifier": [{"value": "PHARM/2024/001"}],
  "status": "issued",
  "type": {
    "coding": [{
      "system": "https://nrces.in/ndhm/fhir/r4/CodeSystem/ndhm-billing-codes",
      "code": "01",
      "display": "Pharmacy"
    }]
  },
  "subject": {"reference": "Patient/example-01"},
  "date": "2024-01-15T14:30:00+05:30",
  "lineItem": [
    {
      "sequence": 1,
      "chargeItemReference": {"reference": "ChargeItem/paracetamol-001"},
      "priceComponent": [
        {
          "type": "base",
          "code": {"coding": [{"code": "01", "display": "Rate"}]},
          "amount": {"value": 80, "currency": "INR"}
        },
        {
          "type": "tax",
          "code": {"coding": [{"code": "03", "display": "CGST"}]},
          "amount": {"value": 4.8, "currency": "INR"}
        },
        {
          "type": "tax",
          "code": {"coding": [{"code": "04", "display": "SGST"}]},
          "amount": {"value": 4.8, "currency": "INR"}
        }
      ]
    },
    {
      "sequence": 2,
      "chargeItemReference": {"reference": "ChargeItem/aspirin-001"},
      "priceComponent": [
        {
          "type": "base",
          "code": {"coding": [{"code": "01", "display": "Rate"}]},
          "amount": {"value": 120, "currency": "INR"}
        },
        {
          "type": "tax",
          "code": {"coding": [{"code": "03", "display": "CGST"}]},
          "amount": {"value": 7.2, "currency": "INR"}
        },
        {
          "type": "tax",
          "code": {"coding": [{"code": "04", "display": "SGST"}]},
          "amount": {"value": 7.2, "currency": "INR"}
        }
      ]
    }
  ],
  "totalNet": {"value": 224, "currency": "INR"},
  "totalGross": {"value": 200, "currency": "INR"}
}
```

## Best Practices

### 1. ChargeItem References
- **Always use ChargeItemReference** (ABDM requirement)
- **Avoid inline codes** in lineItem
- **Create separate ChargeItem** for each billable service/product

### 2. Price Transparency
- **Include MRP** for medications (informational)
- **Show discounts clearly** with separate price components
- **Break down taxes** (CGST, SGST separately)

### 3. Currency and Precision
- **Use INR** as standard currency
- **Maintain precision** for tax calculations (2 decimal places)
- **Round appropriately** following Indian accounting standards

### 4. Validation Rules
- **totalNet = sum of all line item totals including taxes**
- **totalGross = sum of base amounts before taxes**
- **Ensure tax calculations are accurate**

### 5. Integration Patterns
- **Bundle with related resources** (Patient, Practitioner, ChargeItems)
- **Use consistent identifiers** across related resources
- **Maintain audit trail** with proper timestamps

---

**Related Documents:**
- [NDHM FHIR R4 Overview](./NDHM-FHIR-R4-Overview.md)
- [Clinical Profiles Guide](./NDHM-Clinical-Profiles.md)
- [Terminology and Code Systems](./NDHM-Terminology.md)
