/**
 * Profile PIN Utility Functions
 * 
 * This module provides utility functions for managing consent PINs within user profiles.
 * It handles secure PIN storage, hashing, and profile management operations.
 * 
 * Features:
 * - Secure PIN hashing with bcrypt
 * - Profile-based PIN storage
 * - Security field management (lockouts, failed attempts)
 * - Current profile detection and management
 */

const bcrypt = require('bcryptjs');

/**
 * Utility function to set or update consent PIN for current profile
 * 
 * This function handles the secure storage of consent PINs in user profiles.
 * It automatically hashes the PIN using bcrypt and updates the profile with
 * security-related fields.
 * 
 * @param {String} userId - User ID
 * @param {String} newPin - New PIN to set (plain text, will be hashed)
 * @param {Object} options - Additional options
 * @param {Boolean} options.resetSecurity - Whether to reset security fields like failed attempts (default: true)
 * @param {Boolean} options.setPinFlag - Whether to set consentPinSet flag to true (default: true)
 * @returns {Promise<Object>} PIN update result with success status and profile info
 * 
 * @example
 * // Create a new PIN for current profile
 * await setProfileConsentPin(userId, "1234");
 * 
 * // Update PIN without resetting security fields
 * await setProfileConsentPin(userId, "5678", { resetSecurity: false });
 * 
 * // Set PIN but don't mark as "set" (for temporary operations)
 * await setProfileConsentPin(userId, "9999", { setPinFlag: false });
 */
const setProfileConsentPin = async (userId, newPin, options = {}) => {
  try {
    const { User } = require('../../models/user.model');
    
    const {
      resetSecurity = true,
      setPinFlag = true
    } = options;

    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Find current profile based on preferredAbhaAddress
    const profileIndex = user.profiles.findIndex(profile => 
      profile.abhaAddress === user.preferredAbhaAddress
    );

    if (profileIndex === -1) {
      throw new Error('Current profile not found. Please ensure you have a valid ABHA address set.');
    }

    // Hash the new PIN
    const salt = await bcrypt.genSalt(10);
    const hashedPin = await bcrypt.hash(newPin, salt);

    // Update the profile with the new PIN
    user.profiles[profileIndex].consentPin = hashedPin;
    
    if (setPinFlag) {
      user.profiles[profileIndex].consentPinSet = true;
    }
    
    if (resetSecurity) {
      user.profiles[profileIndex].consentPinFailedAttempts = 0;
      user.profiles[profileIndex].consentPinLocked = false;
      user.profiles[profileIndex].consentPinLockedUntil = null;
    }
    
    user.profiles[profileIndex].updatedAt = new Date();

    await user.save();

    return {
      success: true,
      message: 'Consent PIN updated successfully',
      profileAbhaAddress: user.profiles[profileIndex].abhaAddress
    };
  } catch (error) {
    console.error('Error setting profile consent PIN:', error);
    throw new Error('Failed to set profile consent PIN: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Utility function to get current profile for a user
 * 
 * This function retrieves the user's current active profile based on their
 * preferredAbhaAddress. It's useful for operations that need to work with
 * the currently selected profile.
 * 
 * @param {String} userId - User ID
 * @returns {Promise<Object>} Object containing:
 *   - user: Full user document
 *   - profile: Current profile object
 *   - profileIndex: Index of current profile in profiles array
 *   - abhaAddress: ABHA address of current profile
 * 
 * @example
 * const { user, profile, profileIndex } = await getCurrentProfile(userId);
 * console.log(`Current profile: ${profile.firstName} ${profile.lastName}`);
 * console.log(`ABHA Address: ${profile.abhaAddress}`);
 * 
 * // Use profileIndex to update the profile
 * user.profiles[profileIndex].someField = 'new value';
 * await user.save();
 */
const getCurrentProfile = async (userId) => {
  try {
    const { User } = require('../../models/user.model');
    
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Find current profile based on preferredAbhaAddress
    const currentProfile = user.profiles.find(profile => 
      profile.abhaAddress === user.preferredAbhaAddress
    );

    if (!currentProfile) {
      throw new Error('Current profile not found. Please ensure you have a valid ABHA address set.');
    }

    const profileIndex = user.profiles.findIndex(profile => 
      profile.abhaAddress === user.preferredAbhaAddress
    );

    return {
      user,
      profile: currentProfile,
      profileIndex,
      abhaAddress: currentProfile.abhaAddress
    };
  } catch (error) {
    console.error('Error getting current profile:', error);
    throw new Error('Failed to get current profile: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Verify consent PIN for current profile (local verification only)
 *
 * This function verifies a PIN against the current profile's stored PIN.
 * It includes security features like failed attempt tracking and lockouts.
 * This is for local verification only and does not call external APIs.
 *
 * @param {String} userId - User ID
 * @param {String} pin - PIN to verify (plain text)
 * @returns {Promise<Object>} Verification result
 *
 * @example
 * const result = await verifyProfileConsentPin(userId, "1234");
 * if (result.success) {
 *   console.log('PIN verified successfully');
 * } else {
 *   console.log('PIN verification failed:', result.message);
 * }
 */
const verifyProfileConsentPin = async (userId, pin) => {
  try {
    const { user, profile, profileIndex } = await getCurrentProfile(userId);

    // Check if PIN is set for this profile
    if (!profile.consentPinSet || !profile.consentPin) {
      return {
        success: false,
        message: 'Consent PIN not set for this profile. Please create a PIN first.'
      };
    }

    // Check if PIN is locked
    if (profile.consentPinLocked) {
      const now = new Date();
      if (profile.consentPinLockedUntil && now < profile.consentPinLockedUntil) {
        const remainingTime = Math.ceil((profile.consentPinLockedUntil - now) / (1000 * 60));
        return {
          success: false,
          message: `PIN is locked. Try again in ${remainingTime} minutes.`
        };
      } else {
        // Unlock if lock period has expired
        user.profiles[profileIndex].consentPinLocked = false;
        user.profiles[profileIndex].consentPinLockedUntil = null;
        user.profiles[profileIndex].consentPinFailedAttempts = 0;
        await user.save();
      }
    }

    // Verify PIN locally
    const isValidPin = await bcrypt.compare(pin, profile.consentPin);
    if (!isValidPin) {
      // Increment failed attempts
      user.profiles[profileIndex].consentPinFailedAttempts += 1;

      // Lock PIN if too many failed attempts (5 attempts)
      if (user.profiles[profileIndex].consentPinFailedAttempts >= 5) {
        user.profiles[profileIndex].consentPinLocked = true;
        user.profiles[profileIndex].consentPinLockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      }

      await user.save();
      return {
        success: false,
        message: 'Invalid PIN'
      };
    }

    // Reset failed attempts on successful verification
    user.profiles[profileIndex].consentPinFailedAttempts = 0;
    await user.save();

    return {
      success: true,
      message: 'PIN verified successfully',
      profile: profile,
      user: user,
      profileIndex: profileIndex
    };
  } catch (error) {
    console.error('Error verifying profile consent PIN:', error);
    return {
      success: false,
      message: 'Failed to verify PIN: ' + (error.message || 'Unknown error')
    };
  }
};

/**
 * Perform local PIN verification and handle security logic
 *
 * This function handles all the local PIN verification logic including:
 * - Checking if PIN is set
 * - Checking if PIN is locked
 * - Verifying the PIN
 * - Managing failed attempts and lockouts
 * - Unlocking expired locks
 *
 * @param {String} userId - User ID
 * @param {String} pin - PIN to verify (plain text)
 * @returns {Promise<Object>} Verification result with profile data
 */
const performLocalPinVerification = async (userId, pin) => {
  return await verifyProfileConsentPin(userId, pin);
};

/**
 * Get consent PIN status for current profile
 * 
 * @param {String} userId - User ID
 * @returns {Promise<Object>} PIN status information
 */
const getProfileConsentPinStatus = async (userId) => {
  try {
    const { profile } = await getCurrentProfile(userId);

    return {
      success: true,
      hasPinSet: profile.consentPinSet || false,
      isLocked: profile.consentPinLocked || false,
      failedAttempts: profile.consentPinFailedAttempts || 0,
      lockedUntil: profile.consentPinLockedUntil || null,
      profileAbhaAddress: profile.abhaAddress
    };
  } catch (error) {
    console.error('Error getting profile consent PIN status:', error);
    return {
      success: false,
      message: error.message,
      hasPinSet: false,
      isLocked: false
    };
  }
};

/**
 * Example usage of the profile PIN utility functions:
 *
 * // Import the utilities
 * const { setProfileConsentPin, getCurrentProfile, verifyProfileConsentPin, getProfileConsentPinStatus } = require('./profilePinUtils');
 *
 * // Set a new PIN for the current profile
 * try {
 *   await setProfileConsentPin(userId, "1234");
 *   console.log('PIN set successfully');
 * } catch (error) {
 *   console.error('Failed to set PIN:', error.message);
 * }
 *
 * // Verify a PIN
 * const verifyResult = await verifyProfileConsentPin(userId, "1234");
 * if (verifyResult.success) {
 *   console.log('PIN verified successfully');
 * } else {
 *   console.log('PIN verification failed:', verifyResult.message);
 * }
 *
 * // Check PIN status
 * const status = await getProfileConsentPinStatus(userId);
 * console.log('PIN Status:', status);
 *
 * // Get current profile information
 * const { user, profile, profileIndex } = await getCurrentProfile(userId);
 * console.log(`Current profile: ${profile.firstName} ${profile.lastName}`);
 */

module.exports = {
  setProfileConsentPin,
  getCurrentProfile,
  verifyProfileConsentPin,
  performLocalPinVerification,
  getProfileConsentPinStatus
};
