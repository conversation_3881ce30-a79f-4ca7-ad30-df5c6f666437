/**
 * Consent PIN model for storing user consent PINs
 *
 * This model represents the PIN that users set to approve consent requests.
 * The PIN is used to verify the user's identity when approving consent requests.
 *
 * Official ABDM Documentation:
 * https://kiranma72.github.io/abdm-docs/5-building-a-phr-app/managing-consents/index.html
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const bcrypt = require('bcryptjs');

/**
 * Consent PIN schema
 */
const consentPinSchema = new Schema({
  // The user who owns this PIN
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // The ABHA address associated with this PIN
  abhaAddress: {
    type: String,
    required: true
  },
  
  // The hashed PIN
  pin: {
    type: String,
    required: true
  },
  
  // Whether the PIN has been set
  isSet: {
    type: Boolean,
    default: true
  },
  
  // The number of failed attempts to verify the PIN
  failedAttempts: {
    type: Number,
    default: 0
  },
  
  // Whether the PIN is locked due to too many failed attempts
  isLocked: {
    type: Boolean,
    default: false
  },
  
  // The time until which the PIN is locked
  lockedUntil: {
    type: Date
  },
  
  // The creation date of this PIN
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  // The last update date of this PIN
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
consentPinSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Hash the PIN before saving
consentPinSchema.pre('save', async function(next) {
  // Only hash the PIN if it has been modified (or is new)
  if (!this.isModified('pin')) return next();
  
  try {
    // Generate a salt
    const salt = await bcrypt.genSalt(10);
    
    // Hash the PIN with the salt
    this.pin = await bcrypt.hash(this.pin, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare a candidate PIN with the stored hash
consentPinSchema.methods.comparePin = async function(candidatePin) {
  return bcrypt.compare(candidatePin, this.pin);
};

// Create compound unique index for userId and abhaAddress
// This allows multiple PINs per user (one for each profile/ABHA address)
consentPinSchema.index({ userId: 1, abhaAddress: 1 }, { unique: true });

// Create the model
const ConsentPin = mongoose.model('ConsentPin', consentPinSchema);

module.exports = ConsentPin;
