
# ABDM API Documentation

This document provides detailed information about the various endpoints available in the ABDM (Ayushman Bharat Digital Mission) system. Each section describes the purpose, headers, request body, and response body for each endpoint.

## Table of Contents

1. [Base URL and X-CM-ID](#base-url-and-x-cm-id)
2. [Gateway](#gateway)
    1. [Auth Token API](#auth-token-api)
    2. [OpenID Configuration API](#openid-configuration-api)
    3. [Keycloak Certificate API](#keycloak-certificate-api)
    4. [Update Bridge URL API](#update-bridge-url-api)
    5. [Registration of Facility & Software Linkage](#registration-of-facility--software-linkage)
    6. [Find Bridge by Service ID](#find-bridge-by-service-id)
    7. [Find Services by Bridge ID](#find-services-by-bridge-id)
3. [HIP Initiated Linking](#hip-initiated-linking)
    1. [Link Token Generation](#link-token-generation)
    2. [Callback API for Link Token Generation](#callback-api-for-link-token-generation)
    3. [Linking Care Context](#linking-care-context)
    4. [Callback API for Linking Care Context](#callback-api-for-linking-care-context)
    5. [Get All Patient Links](#get-all-patient-links)
    6. [Notify Care Context Update](#notify-care-context-update)
    7. [Callback API for Notify Care Context Update](#callback-api-for-notify-care-context-update)
    8. [SMS Notification to Patients](#sms-notification-to-patients)
    9. [Callback API for SMS Notification to Patients](#callback-api-for-sms-notification-to-patients)
4. [User Initiated Linking](#user-initiated-linking)
    1. [Patient Health Record Discovery](#patient-health-record-discovery)
    2. [HIE-CM Callback to HIP - Discovery](#hie-cm-callback-to-hip---discovery)
    3. [HMIS/LMIS Response on Health Record Discovery](#hmislmis-response-on-health-record-discovery)
    4. [HIE-CM Callback on Health Record Discovery](#hie-cm-callback-on-health-record-discovery)
    5. [Patient Health Record Link Init](#patient-health-record-link-init)
    6. [HIE-CM Callback on Health Record Link Init](#hie-cm-callback-on-health-record-link-init)
    7. [HMIS/LMIS Response on Health Record Link](#hmislmis-response-on-health-record-link)
    8. [HIE-CM Response on Health Record Link](#hie-cm-response-on-health-record-link)
    9. [Patient Health Record Confirm](#patient-health-record-confirm)
    10. [HIE-CM Callback for Health Record Confirmation](#hie-cm-callback-for-health-record-confirmation)
    11. [HMIS/LMIS Response on Health Record Confirm](#hmislmis-response-on-health-record-confirm)
    12. [HIE-CM Response on Health Record Confirm](#hie-cm-response-on-health-record-confirm)
5. [Data Flow](#data-flow)
    1. [Callback API to HIP when a consent request is APPROVED/REVOKED](#callback-api-to-hip-when-a-consent-request-is-approvedrevoked)
    2. [HIP to respond back to consent HIP notify](#hip-to-respond-back-to-consent-hip-notify)
    3. [Data Flow - Health information request - Callback to HIP](#data-flow---health-information-request---callback-to-hip)
    4. [HIP acknowledgement to the health information request](#hip-acknowledgement-to-the-health-information-request)
    5. [HIP calling data push URL](#hip-calling-data-push-url)
    6. [Health Information notify API](#health-information-notify-api)
6. [Scan and Profile Share](#scan-and-profile-share)
    1. [Profile Share](#profile-share)
    2. [Profile Share - Callback](#profile-share---callback)
    3. [Profile on-share](#profile-on-share)
    4. [Profile on share - Callback](#profile-on-share---callback)
7. [API Listing](#api-listing)
8. [Error Codes Listing](#error-codes-listing)

## Base URL and X-CM-ID

| Environment | Base URL | X-CM-ID |
| --- | --- | --- |
| Sandbox | https://dev.abdm.gov.in | Sbx |
| Production | https://apis.abdm.gov.in | Abdm |

## Gateway

### Auth Token API

**Purpose:** This API is used to generate an auth token.

**URL:** `/api/hiecm/gateway/v3/sessions`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | Shs | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "clientId": "SBX_XXXXXX",
    "clientSecret": "XXXX-XXX-XXXX-XXXX-XXXXXXX",
    "grantType": "client_credentials"
}
```

**Response:**

```json
{
    "accessToken": "eyJhbGciOUSUzI1NiIsInR5cCIgOiAiSldUliwia2lkliA6ICJBbFJINVdDbThUbTIFSl9JZk85ejA2ajlvQ3Y1MXBLS 0ZrbkdiX1RCdkswIn0...",
    "expiresIn": 1200,
    "refreshExpiresIn": 1800,
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUliwia2lkliA6ICIyMWU5NzA4OS00ZTcxLTQyNGEtOTAzYS1jOTAyMW M1NmFINWYifQ...",
    "tokenType": "bearer"
}
```

### OpenID Configuration API

**Purpose:** Provides configuration information about the Identity Provider (IDP).

**URL:** `/api/hiecm/gateway/v3/.well-known/openid-configuration`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | Sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Response:**

```json
{
    "jwks_uri": "https://dev.abdm.gov.in/api/hiecm/gateway/v3/certs"
}
```

### Keycloak Certificate API

**Purpose:** Provides an OAuth certificate that can be used with open-source authentication requests for certificates.

**URL:** `/api/hiecm/gateway/v3/certs`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | $2022-10-06 T 10: 10: 00.587 Z$ | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| K-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Response:**

```json
{
    "keys": [
        {
            "e": "AQAB",
            "kid": "AlRb5WCm8Tm9EJ_IfO9z06j9oCv51pKKFknGb_TBvK0",
            "kty": "RSA",
            "n": "mgmW7W5ZGF_G5cJevwYi8HiPcl-6qS_psnZxa4v3bkwAkyOoOd8-6ketrOIZA2PbRbGnxFfZHiI94rdFXJ4Q9ampscsz9NocTIPMPmWydJ8A50pZaYWyikYDSJiDltq7i3WspPKSOuQHrC 5h9dMcCVveX5oeg0tO68Z79gwDlpcxiqDbFaphsqDvx-5XkfwiqvOBaybK6_8CBPuTqWMUEuUkILYXu2X7ESHdVNFMFAjxCcCXUtP7LFdvT3nnFekRmG82QbSQSVe 4N5tPH8q0MCxSWWn2c15bDnzOF-dvfRCVPRabCzw0M-utHR9diTrWtq6Koi5buxgwM1rbk0p8Q",
            "use": "sig",
            "x5c": [
                "MIICrzCCAZcC8gFy/3WZBjANBgkqhkiG9w0BAQsFADAbMRkwFwYDVQQDDBBjZW50cmFsLXJlZ2lzdHJ5MB4XDTIwMDYyOTASNDEzNloXDTMwMDYyOTASNDMxNlowGzEZMBcGA1UEAwwQY2VudHJhbC1yZWdpc3RyeTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJoJlu1uWRhfxuXCXr8GIvB4j3CPuqkv6b J2cWuL925MAJMjqDnfPupHraziPmQNj20Wxp8RX2R4iPeK3RVyeEPWpqbHLM/TaHEyDzD5lsnSfAOdKWW mFsopGA0iYg5bau4t1rKTykjrkB6wuYfXTHAlb3I+aHoNLTuvGe/YMA5aXMYqg2xWqYbKg78fuV5H8Iqrzg WsmyuvwQgT7k6ljFBLUJS2F7tl+xEh3VTRTBQI8QnAl1LT+yxXb0955xXpEZhvNkG0kEIXuDebTx/KtDAsUllp9nNeWw58zhfnb30QIT0Wmws8NDPrrR0fXYk61rauiqIuW7sYMDNa25NKfECAwEAATANBgkqhkiG9w0BAQs FAAOCAQEACkC3TijrXlgi4vn+l1uL1nfdK6vOIL5UZ6yCjSOq7zYW6b3Qe8j7NrPb9RJC+pbIERyNbB+t9hsa5 g1L7lkjCNlUuxfJprsJ9LJKlM5g7dYEA6XPCJ7C6AVlarj72vlWXQvwjnQMO2/CM9/Jp5Hnv2Qwjn7NME2OW M0iblc/TD+DEZK5L5mIWMyuBSQo2o/AcOmfG4MoE5Gm/CaOJ47rSrf+Iq83e5+dyKh7uLVAa+5WK8Im 5nEs6BLSGyo2KlaV0mW9yCkoRLLbipjH8+rJwkUU6iu7QVjz0peGZzYldya5n35gMWH7Bu4HqFneKNRww D6w8rGNC+uWtgWejDZ3yQ=="
            ],
            "x5t": "EaMhYGUIvMkp8tvSM3QoaqaF8xM",
            "x5t2": "vGer6Pt8AhZn8RIbHhAFksOCcGf3u1UWU7Qq-Doy7ro",
            "alg": "RS256"
        }
    ]
}
```

### Update Bridge URL API

**Purpose:** This API is used to update the bridge base URL.

**URL:** `/api/hiecm/gateway/v3/bridge/url`

**Method:** PATCH

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "url": "https://webhook.site/b799c0b8-4e75-4545-8eb2-d8c2d0f0c9f6"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Registration of Facility & Software Linkage

**Purpose:** This API is used to link multiple bridges against a facility.

**URL:** `https://facilitysbx.abdm.gov.in/v1/bridges/MutipleHRPAddUpdateServices`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "facilityId": "INXXXXXXXXXX",
    "facilityName": "Facility Name",
    "bridgeId": "SBX_XXXXXX",
    "hipName": "Hospital Name",
    "type": "HIP",
    "active": true
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Find Bridge by Service ID

**Purpose:** This API fetches the bridge details for the given service ID.

**URL:** `/api/hiecm/gateway/v3/bridge-service/serviceld/{serviceld}`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Response:**

```json
{
    "bridge": {
        "id": "SBX_XXXX",
        "name": "Testing",
        "url": "https://abdcb.doctor9.com",
        "active": true,
        "blocklisted": false
    }
}
```

### Find Services by Bridge ID

**Purpose:** This API fetches all the service ID details linked with the respective bridge ID.

**URL:** `/api/hiecm/gateway/v3/bridge-services`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Response:**

```json
{
    "bridge": {
        "id": "SBX_XXXX",
        "name": "Testing",
        "url": "https://abdcb.doctor9.com",
        "active": true,
        "blocklisted": false
    },
    "services": [
        {
            "id": "@#$%^&*{",
            "name": "hello",
            "types": [
                "HIP",
                "HIU"
            ],
            "endpoints": {
                "hipEndpoints": [
                    {
                        "use": "registration",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/registration"
                    },
                    {
                        "use": "data-upload",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/dataupload"
                    }
                ],
                "hiuEndpoints": [
                    {
                        "use": "registration",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/registration"
                    },
                    {
                        "use": "data-upload",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/dataupload"
                    }
                ],
                "healthLockerEndpoints": [
                    {
                        "use": "registration",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/registration"
                    },
                    {
                        "use": "data-upload",
                        "connectionType": "HTTPS",
                        "address": "https://events.hookdeck.com/e/src_3gsnEgl941mh/dataupload"
                    }
                ]
            },
            "active": true
        }
    ]
}
```

## HIP Initiated Linking

### Link Token Generation

**Purpose:** This API is used to generate a link token.

**URL:** `/api/hiecm/v3/token/generate-token`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "abhaAddress": "ABHA address",
    "abhaNumber": "ABHA number",
    "name": "Full name",
    "gender": "M/F/O",
    "yearOfBirth": "XXXX"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Callback API for Link Token Generation

**Purpose:** This is a callback API triggered by HIE-CM to HIP/HRP to get the link token.

**URL:** `{callback_url}/api/v3/hip/token/on-generate-token`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "abhaAddress": "10000262131640@sbx",
    "linkToken": "eyJhbGciOiJSUzUxMiJ9...",
    "response": {
        "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Linking Care Context

**Purpose:** This API is used to link the care context against the patient ABHA address.

**URL:** `/api/hiecm/hip/v3/link/carecontext`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| X-LINK-TOKEN | eyJhbGciOiJSUzUxMiJ9... | Yes | Link token generated against patient ABHA address and/or ABHA number. |

**Request Body:**

```json
{
    "abhaNumber": "9117838610XXXX",
    "abhaAddress": "abc@abdm",
    "patient": [
        {
            "referenceNumber": "TMH-PUID-001",
            "display": "Display",
            "careContexts": [
                {
                    "referenceNumber": "TMH-PUID",
                    "display": "display 1"
                }
            ],
            "hiType": "PRESCRIPTION",
            "count": 1
        }
    ]
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Callback API for Linking Care Context

**Purpose:** This is a callback API triggered by HIE-CM to notify HIP/HRP about linked care context response.

**URL:** `{callback_url}/api/v3/link/on_carecontext`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "abhaAddress": "abc@sbx",
    "status": "Successfully Linked care context",
    "response": {
        "requestId": "f29f0e59-8388-4698-9fe6-05db67aeac46"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Get All Patient Links

**Purpose:** This API provides all the linked care context of the patient.

**URL:** `/api/hiecm/hip/v3/link/patient/links`

**Method:** GET

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2023-03-09T07:07:41.793Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-AUTH-TOKEN | eyJhbGciOiJSUzUxMiJ9... |  | JWT Authentication token which was issued by ABDM after successful validation of username and password. |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Params:**

| Property Name | Example Value | Description |
| --- | --- | --- |
| limit | 100 | Number of records to be fetched from the database. |

**Response:**

```json
{
    "patient": {
        "id": "user_1992@sbx",
        "links": [
            {
                "hip": {
                    "id": "TestClinicHIP",
                    "name": "TestClinicHIP",
                    "type": "HIP"
                },
                "referenceNumber": "user_1992@sbx",
                "display": "User Record",
                "hiType": "HealthDocumentRecord",
                "careContexts": [
                    {
                        "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                        "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
                    }
                ],
                "dateCreated": "2024-07-18T11:49:15.736Z"
            }
        ]
    }
}
```

### Notify Care Context Update

**Purpose:** This API is used to notify all the subscribed HIUs after updating a health record.

**URL:** `/api/hiecm/hip/v3/link/context/notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2023-03-09T07:07:41.793Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 |  | Identifier of the health information provider to which the request was intended. |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "notification": {
        "patient": {
            "id": "user_122@sbx"
        },
        "careContext": {
            "patientReference": "batman@tmh",
            "careContextReference": "Episode1"
        },
        "hiTypes": [
            "OPConsultation"
        ],
        "date": "2024-05-30T05:21:34.155Z",
        "hip": {
            "id": "demo-hip-261222"
        }
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Callback API for Notify Care Context Update

**Purpose:** This is a callback API triggered by HIE-CM to notify HIP/HRP about care context update response.

**URL:** `{callbackURL}/api/v3/links/context/on-notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2023-03-09T07:07:41.793Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |

**Request Body:**

```json
{
    "requestId": "743ec386-670f-43a8-a3ed-44aa30fb15fb",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "acknowledgement": {
        "status": "SUCCESS"
    },
    "response": {
        "requestId": "6f0b4665-a915-4c92-aa36-65afb4a2cd71"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### SMS Notification to Patients

**Purpose:** This API is used to trigger an SMS notification to the patient's mobile number when a health record is available to fetch.

**URL:** `/api/hiecm/hip/v3/link/patient/links/sms/notify2`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2023-03-09T07:07:41.793Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |

**Request Body:**

```json
{
    "requestId": "743ec386-670f-43a8-a3ed-44aa30fb15fb",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "notification": {
        "phoneNo": "986543210",
        "hip": {
            "id": "ABDM_HIP",
            "name": "ABC Hospital"
        }
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Callback API for SMS Notification to Patients

**Purpose:** This is a callback API triggered by HIE-CM to notify HIP/HRP about SMS notification response.

**URL:** `{callbackURL}/api/v3/patients/sms/on-notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2023-03-09T07:07:41.793Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |

**Request Body:**

```json
{
    "requestId": "743ec386-670f-43a8-a3ed-44aa30fb15fb",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "status": "SUCCESS",
    "error": {
        "code": "ABDM-1024",
        "message": "Dependent service unavailable"
    },
    "resp": {
        "requestId": "6f0b4665-a915-4c92-aa36-65afb4a2cd71"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

## User Initiated Linking

### Patient Health Record Discovery

**Purpose:** This API is used by the patient/user from the PHR application to HIECM to discover his/her health records.

**URL:** `/api/hiecm/user-initiated-linking/v3/patient/care-context/discover`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2023-03-09T07:07:41.793Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-AUTH-TOKEN | eyJhbGciOiJSUzUxMiJ9... |  | JWT Authentication token which was issued by ABDM after successful validation of username and password. |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| X-HIU-ID | HIU_ID | Yes | Identifier of the health information user to which the request was intended. |

**Request Body:**

```json
{
    "hipId": "ABDM_HIP",
    "unverifiedIdentifiers": [
        {
            "type": "ABHA_ADDRESS",
            "value": "shaik.XXXX@sbx"
        }
    ]
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM Callback to HIP - Discovery

**Purpose:** This is a callback API triggered by HIE-CM to HIP to get the discovery response.

**URL:** `{callback_url}/api/v3/hip/discovery/on-discover`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HMIS/LMIS Response on Health Record Discovery

**Purpose:** This API is used by HMIS/LMIS to respond to the health record discovery request.

**URL:** `{callback_url}/api/v3/hip/discovery/on-discover`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM Callback on Health Record Discovery

**Purpose:** This is a callback API triggered by HIE-CM to notify the patient about the health record discovery response.

**URL:** `{callback_url}/api/v3/hip/discovery/on-discover`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Patient Health Record Link Init

**Purpose:** This API is used to initiate the linking of the patient's health record.

**URL:** `/api/hiecm/user-initiated-linking/v3/patient/care-context/link-init`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2023-03-09T07:07:41.793Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-AUTH-TOKEN | eyJhbGciOiJSUzUxMiJ9... |  | JWT Authentication token which was issued by ABDM after successful validation of username and password. |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| X-HIU-ID | HIU_ID | Yes | Identifier of the health information user to which the request was intended. |

**Request Body:**

```json
{
    "hipId": "ABDM_HIP",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM Callback on Health Record Link Init

**Purpose:** This is a callback API triggered by HIE-CM to notify the patient about the health record link initiation response.

**URL:** `{callback_url}/api/v3/hip/link-init/on-init`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HMIS/LMIS Response on Health Record Link

**Purpose:** This API is used by HMIS/LMIS to respond to the health record link request.

**URL:** `{callback_url}/api/v3/hip/link-init/on-init`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM Response on Health Record Link

**Purpose:** This is a callback API triggered by HIE-CM to notify the patient about the health record link response.

**URL:** `{callback_url}/api/v3/hip/link-init/on-init`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Patient Health Record Confirm

**Purpose:** This API is used to confirm the linking of the patient's health record.

**URL:** `/api/hiecm/user-initiated-linking/v3/patient/care-context/confirm`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2023-03-09T07:07:41.793Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-AUTH-TOKEN | eyJhbGciOiJSUzUxMiJ9... |  | JWT Authentication token which was issued by ABDM after successful validation of username and password. |
| X-CM-ID | sbx | Yes | Suffix of the consent manager to which the request was intended. |
| X-HIU-ID | HIU_ID | Yes | Identifier of the health information user to which the request was intended. |

**Request Body:**

```json
{
    "hipId": "ABDM_HIP",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM Callback for Health Record Confirmation

**Purpose:** This is a callback API triggered by HIE-CM to notify the patient about the health record confirmation response.

**URL:** `{callback_url}/api/v3/hip/confirm/on-confirm`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HMIS/LMIS Response on Health Record Confirm

**Purpose:** This API is used by HMIS/LMIS to respond to the health record confirmation request.

**URL:** `{callback_url}/api/v3/hip/confirm/on-confirm`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIE-CM Response on Health Record Confirm

**Purpose:** This is a callback API triggered by HIE-CM to notify the patient about the health record confirmation response.

**URL:** `{callback_url}/api/v3/hip/confirm/on-confirm`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

## Data Flow

### Callback API to HIP when a consent request is APPROVED/REVOKED

**Purpose:** This is a callback API triggered by HIE-CM to notify HIP about the consent request status.

**URL:** `{callback_url}/api/v3/consent/on-notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "consentRequestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "status": "APPROVED/REVOKED"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIP to respond back to consent HIP notify

**Purpose:** This API is used by HIP to respond back to the consent notification.

**URL:** `{callback_url}/api/v3/consent/on-notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "consentRequestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "status": "ACKNOWLEDGED"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Data Flow - Health information request - Callback to HIP

**Purpose:** This is a callback API triggered by HIE-CM to notify HIP about the health information request.

**URL:** `{callback_url}/api/v3/health-information/on-request`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIP acknowledgement to the health information request

**Purpose:** This API is used by HIP to acknowledge the health information request.

**URL:** `{callback_url}/api/v3/health-information/on-request`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "status": "ACKNOWLEDGED"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### HIP calling data push URL

**Purpose:** This API is used by HIP to push data to the specified URL.

**URL:** `{data_push_url}`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "patient": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Record",
        "careContexts": [
            {
                "referenceNumber": "e707c945-3672-4b85-8525-4c7e620ef301",
                "display": "Visited on 08-Feb-2024 09:00:00 Visit Type as Out Patient"
            }
        ],
        "hiType": "HealthDocumentRecord"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Health Information notify API

**Purpose:** This API is used by HIP to notify about the health information.

**URL:** `{callback_url}/api/v3/health-information/on-notify`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "transactionId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "status": "SUCCESS"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

## Scan and Profile Share

### Profile Share

**Purpose:** This API is used to share the profile.

**URL:** `/api/hiecm/profile/v3/share`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "profile": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Profile"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Profile Share - Callback

**Purpose:** This is a callback API triggered by HIE-CM to notify about the profile share response.

**URL:** `{callback_url}/api/v3/profile/on-share`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "status": "SUCCESS"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Profile on-share

**Purpose:** This API is used to handle the profile on-share event.

**URL:** `/api/hiecm/profile/v3/on-share`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "profile": {
        "referenceNumber": "user_1992@sbx",
        "display": "User Profile"
    }
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

### Profile on share - Callback

**Purpose:** This is a callback API triggered by HIE-CM to notify about the profile on-share response.

**URL:** `{callback_url}/api/v3/profile/on-share`

**Method:** POST

**Headers:**

| Property Name | Example Value | Required | Description |
| --- | --- | --- | --- |
| REQUEST-ID | 18235d89-cb13-479d-ad71-7a57d5f669a8 | Yes | Unique UUID for tracking the end-to-end request transaction. |
| TIMESTAMP | 2022-10-06T10:10:00.587Z | Yes | The actual time when the request was initiated, ISO Date time format represents the date and time. |
| X-HIP-ID | IN2810014366 | Yes | Identifier of the health information provider to which the request was intended. |
| Authorization | eyJhbGciOiJSUzUxMiJ9... | Yes | JWT Access token which was issued by ABDM session API after successful validation of client id and secret. |

**Request Body:**

```json
{
    "requestId": "d6d6d056-666a-4af8-b680-4c61bcb29dd4",
    "timestamp": "2024-05-09T10:34:00.387Z",
    "status": "SUCCESS"
}
```

**Response:**

```json
{
    "status": "202 Accepted"
}
```

## API Listing

This section lists all the APIs available in the ABDM system. The detailed documentation for each API can be found in the respective sections above.

## Error Codes Listing

This section lists all the error codes that can be encountered while using the ABDM APIs. Each error code is associated with a specific error scenario.

| Error Code | Description |
| --- | --- |
| ABDM-1030 | Invalid request ID |
| ABDM-1016 | Invalid Timestamp |
| ABDM-1064 | Request body was missing |
| ABDM-1092 | Duplicate Link token request |
| ABDM-9999 | Invalid ABHA Number, it must be only 14 digit |
| ABDM-9999 | Invalid ABHA Address, it must start with Alphanumeric . and _ in the middle and must be ending with @abdm or @sbx |
| ABDM-1207 | Demographic details was invalid or doesn't exists |
| ABDM-9999 | Invalid Gender, It must be M, F, O, D |
| ABDM-9999 | Invalid Year of birth, must be 4 digit range between 1900 and 2200 |
| ABDM-1063 | HIP Id mismatch with Link token |
| ABDM-1038 | ABHA address mismatch with Link token |
| ABDM-1062 | ABHA number mismatch with Link token |
| ABDM-9999 | careContexts attribute required in the payload |
| ABDM-9999 | Invalid HiType, it must be in PRESCRIPTION, DIAGNOSTIC REPORT, OPCONSULTATION, DISCHARGE SUMMARY, IMMUNIZATIONRECORD, HEALTHDOCUMENTRECORD, WELLNESSRECORD |
| ABDM-1037 | Count and Care context count mismatch |
| ABDM-1066 | Invalid JWT token |
| ABDM-1035 | Invalid HIP ID |
| ABDM-1024 | Dependent service unavailable |
| ABDM-1115 | Invalid patient information. At least one patient information is required. |
| ABDM-1031 | The abha address is deactivated. |
