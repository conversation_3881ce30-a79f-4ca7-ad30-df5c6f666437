# ABDM ABHA V3 API Documentation - Comprehensive Guide

## Overview

This document provides comprehensive documentation for the Ayushman Bharat Digital Mission (ABDM) ABHA V3 APIs. These APIs enable the creation, verification, and management of ABHA (Ayushman Bharat Health Account) IDs, which serve as unique health identifiers for citizens in India.

The documentation covers all major API endpoints including ABHA creation, verification, profile management, linking, and health record access.

## Base URLs

- **Sandbox Environment**: `https://dev.abdm.gov.in/api`
- **Production Environment**: `https://abdm.gov.in/api`

## Common Headers

All API requests must include the following headers:

| Header Name | Description | Example |
|-------------|-------------|---------|
| Content-Type | Media type of the request body | `application/json` |
| X-CM-ID | Consent Manager ID | `sbx` (for sandbox), `abdm` (for production) |
| REQUEST-ID | Unique request identifier (UUID) | `c5a5c09c-ea0e-4fec-a9bc-27c7cf626168` |
| TIMESTAMP | Current timestamp in ISO 8601 format | `2024-10-11T20:18:30.731Z` |
| Authorization | Bearer token for authenticated requests | `Bearer eyJhbGciOiJSUzI1...` |

## Authentication APIs

### 1. Generate Session Token

Creates a session token required for subsequent API calls.

**Endpoint**: `POST /hiecm/gateway/v3/sessions`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm

**Request Body**:
```json
{
    "clientId": "your-client-id",
    "clientSecret": "your-client-secret",
    "grantType": "client_credentials"
}
```

**Response**:
```json
{
    "accessToken": "eyJhbGciOiJSUzI1...",
    "expiresIn": 600,
    "refreshToken": "eyJhbGciOiJSUzI1...",
    "refreshExpiresIn": 1800,
    "tokenType": "bearer"
}
```

**Purpose**: Authenticates the client application and provides access tokens for subsequent API calls.

## ABHA Creation APIs

### 2. Generate Mobile OTP

Initiates the ABHA creation process by sending an OTP to the user's mobile number.

**Endpoint**: `POST /hiecm/gateway/v3/abha/registration/mobile/otp`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "mobile": "9XXXXXXXXX"
}
```

**Response**:
```json
{
    "txnId": "a77e645c-9c10-4c84-8750-86d9c6548328"
}
```

**Purpose**: Initiates the ABHA creation process by sending an OTP to the provided mobile number.

### 2.1 Generate Aadhaar OTP

Initiates the ABHA creation process by sending an OTP to the Aadhaar-linked mobile number.

**Endpoint**: `POST /hiecm/gateway/v3/abha/registration/aadhaar/otp`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "aadhaar": "XXXX XXXX XXXX",
    "consent": true
}
```

**Response**:
```json
{
    "txnId": "d88f645c-9c10-4c84-8750-86d9c6548123"
}
```

**Purpose**: Initiates the ABHA creation process by sending an OTP to the Aadhaar-linked mobile number.

### 3. Verify Mobile OTP

Verifies the OTP sent to the user's mobile number.

**Endpoint**: `POST /hiecm/gateway/v3/abha/registration/mobile/otp/verify`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "otp": "123456",
    "txnId": "a77e645c-9c10-4c84-8750-86d9c6548328"
}
```

**Response**:
```json
{
    "txnId": "a77e645c-9c10-4c84-8750-86d9c6548328",
    "mobileLinked": true
}
```

**Purpose**: Verifies the OTP sent to the user's mobile number and confirms mobile verification.

### 4. Register ABHA with Aadhaar

Creates an ABHA ID using Aadhaar verification.

**Endpoint**: `POST /hiecm/gateway/v3/abha/registration/aadhaar`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "txnId": "a77e645c-9c10-4c84-8750-86d9c6548328",
    "aadhaar": "XXXX XXXX XXXX",
    "consent": true
}
```

**Response**:
```json
{
    "txnId": "b88f645c-9c10-4c84-8750-86d9c6548456"
}
```

**Purpose**: Initiates ABHA creation using Aadhaar verification.

### 5. Verify Aadhaar OTP

Verifies the OTP sent to the Aadhaar-linked mobile number.

**Endpoint**: `POST /hiecm/gateway/v3/abha/registration/aadhaar/otp/verify`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "otp": "123456",
    "txnId": "b88f645c-9c10-4c84-8750-86d9c6548456"
}
```

**Response**:
```json
{
    "txnId": "b88f645c-9c10-4c84-8750-86d9c6548456",
    "name": "John Doe",
    "gender": "M",
    "yearOfBirth": "1990",
    "monthOfBirth": "01",
    "dayOfBirth": "01",
    "address": {
        "line": "123 Main St",
        "district": "Bangalore",
        "state": "Karnataka",
        "pincode": "560001"
    },
    "email": "<EMAIL>",
    "mobile": "9XXXXXXXXX"
}
```

**Purpose**: Verifies the Aadhaar OTP and returns user demographic information.

### 6. Create ABHA Address

Creates an ABHA address for the user.

**Endpoint**: `POST /hiecm/gateway/v3/abha/registration/phr-address`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "txnId": "b88f645c-9c10-4c84-8750-86d9c6548456",
    "phrAddress": "johndoe@sbx",
    "password": "Password@123",
    "firstName": "John",
    "lastName": "Doe",
    "middleName": "",
    "email": "<EMAIL>",
    "healthId": "johndoe@sbx",
    "profilePhoto": "base64-encoded-image"
}
```

**Response**:
```json
{
    "phrAddress": "johndoe@sbx",
    "token": "eyJhbGciOiJSUzI1...",
    "refreshToken": "eyJhbGciOiJSUzI1..."
}
```

**Purpose**: Creates an ABHA address for the user and returns authentication tokens.

## ABHA Verification APIs

### 7. Verify ABHA ID

Verifies an existing ABHA ID.

**Endpoint**: `POST /hiecm/gateway/v3/abha/verify`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "abhaNumber": "XX-XXXX-XXXX-XXXX"
}
```

**Response**:
```json
{
    "txnId": "c99f645c-9c10-4c84-8750-86d9c6548789",
    "authMethods": ["MOBILE_OTP", "AADHAAR_OTP"]
}
```

**Purpose**: Verifies an ABHA ID and returns available authentication methods.

### 7.1 Verify ABHA Address

Verifies an existing ABHA address.

**Endpoint**: `POST /hiecm/gateway/v3/abha/phr-address/verify`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "phrAddress": "johndoe@sbx"
}
```

**Response**:
```json
{
    "txnId": "e99f645c-9c10-4c84-8750-86d9c6548123",
    "authMethods": ["MOBILE_OTP", "PASSWORD"]
}
```

**Purpose**: Verifies an ABHA address and returns available authentication methods.

### 8. Generate Auth OTP

Generates an OTP for ABHA authentication.

**Endpoint**: `POST /hiecm/gateway/v3/abha/auth/otp`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "txnId": "c99f645c-9c10-4c84-8750-86d9c6548789",
    "authMethod": "MOBILE_OTP"
}
```

**Response**:
```json
{
    "txnId": "c99f645c-9c10-4c84-8750-86d9c6548789"
}
```

**Purpose**: Generates an OTP for ABHA authentication using the specified method.

### 9. Verify Auth OTP

Verifies the OTP for ABHA authentication.

**Endpoint**: `POST /hiecm/gateway/v3/abha/auth/otp/verify`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "txnId": "c99f645c-9c10-4c84-8750-86d9c6548789",
    "otp": "123456"
}
```

**Response**:
```json
{
    "txnId": "c99f645c-9c10-4c84-8750-86d9c6548789",
    "abhaNumber": "XX-XXXX-XXXX-XXXX",
    "name": "John Doe",
    "gender": "M",
    "yearOfBirth": "1990",
    "monthOfBirth": "01",
    "dayOfBirth": "01",
    "address": {
        "line": "123 Main St",
        "district": "Bangalore",
        "state": "Karnataka",
        "pincode": "560001"
    },
    "mobile": "9XXXXXXXXX",
    "email": "<EMAIL>"
}
```

**Purpose**: Verifies the OTP and returns user information associated with the ABHA ID.

## Link Token APIs

### 10. Generate Link Token

Generates a link token for connecting ABHA ID with healthcare providers.

**Endpoint**: `POST /hiecm/gateway/v3/abha/link/token`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "abhaNumber": "XX-XXXX-XXXX-XXXX"
}
```

**Response**:
```json
{
    "token": "eyJhbGciOiJSUzI1...",
    "expiry": 1800
}
```

**Purpose**: Generates a link token that can be used to connect the ABHA ID with healthcare providers.

### 10.1 Generate Link Token for ABHA Address

Generates a link token for connecting ABHA Address with healthcare providers.

**Endpoint**: `POST /hiecm/gateway/v3/abha/phr-address/link/token`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "phrAddress": "johndoe@sbx"
}
```

**Response**:
```json
{
    "token": "eyJhbGciOiJSUzI1...",
    "expiry": 1800
}
```

**Purpose**: Generates a link token that can be used to connect the ABHA Address with healthcare providers.

## ABHA Profile Management APIs

### 11. Get ABHA Profile

Retrieves the profile information associated with an ABHA ID.

**Endpoint**: `GET /hiecm/gateway/v3/abha/profile`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token
- X-TOKEN: Link token from previous API

**Response**:
```json
{
    "abhaNumber": "XX-XXXX-XXXX-XXXX",
    "name": "John Doe",
    "gender": "M",
    "yearOfBirth": "1990",
    "monthOfBirth": "01",
    "dayOfBirth": "01",
    "address": {
        "line": "123 Main St",
        "district": "Bangalore",
        "state": "Karnataka",
        "pincode": "560001"
    },
    "mobile": "9XXXXXXXXX",
    "email": "<EMAIL>",
    "profilePhoto": "base64-encoded-image",
    "kycStatus": "VERIFIED",
    "kycVerifiedDate": "2023-01-01T12:00:00Z",
    "healthIdNumber": "XX-XXXX-XXXX-XXXX",
    "phrAddresses": ["johndoe@sbx"]
}
```

**Purpose**: Retrieves the complete profile information associated with an ABHA ID.

### 12. Update ABHA Profile

Updates the profile information associated with an ABHA ID.

**Endpoint**: `PATCH /hiecm/gateway/v3/abha/profile`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token
- X-TOKEN: Link token from previous API

**Request Body**:
```json
{
    "email": "<EMAIL>",
    "profilePhoto": "base64-encoded-image"
}
```

**Response**:
```json
{
    "abhaNumber": "XX-XXXX-XXXX-XXXX",
    "name": "John Doe",
    "gender": "M",
    "yearOfBirth": "1990",
    "monthOfBirth": "01",
    "dayOfBirth": "01",
    "address": {
        "line": "123 Main St",
        "district": "Bangalore",
        "state": "Karnataka",
        "pincode": "560001"
    },
    "mobile": "9XXXXXXXXX",
    "email": "<EMAIL>",
    "profilePhoto": "base64-encoded-image",
    "kycStatus": "VERIFIED",
    "kycVerifiedDate": "2023-01-01T12:00:00Z",
    "healthIdNumber": "XX-XXXX-XXXX-XXXX",
    "phrAddresses": ["johndoe@sbx"]
}
```

**Purpose**: Updates specific fields in the ABHA profile and returns the updated profile information.

### 12.1 Generate ABHA Card

Generates an ABHA card for a user.

**Endpoint**: `POST /hiecm/gateway/v3/abha/card`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token
- X-TOKEN: Link token from previous API

**Request Body**:
```json
{
    "abhaNumber": "XX-XXXX-XXXX-XXXX"
}
```

**Response**: Binary PDF content with appropriate Content-Type header.

**Purpose**: Generates a downloadable ABHA card in PDF format.

### 12.2 Generate QR Code

Generates a QR code for an ABHA ID.

**Endpoint**: `POST /hiecm/gateway/v3/abha/qrcode`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token
- X-TOKEN: Link token from previous API

**Request Body**:
```json
{
    "abhaNumber": "XX-XXXX-XXXX-XXXX"
}
```

**Response**: Binary image content with appropriate Content-Type header.

**Purpose**: Generates a QR code image for the ABHA ID that can be scanned by healthcare providers.

### 12.3 Change Mobile Number

Initiates the process to change the mobile number associated with an ABHA ID.

**Endpoint**: `POST /hiecm/gateway/v3/abha/profile/update/mobile/init`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token
- X-TOKEN: Link token from previous API

**Request Body**:
```json
{
    "mobile": "9XXXXXXXXX"
}
```

**Response**:
```json
{
    "txnId": "f77e645c-9c10-4c84-8750-86d9c6548987"
}
```

**Purpose**: Initiates the process to change the mobile number associated with an ABHA ID.

## Error Handling

All APIs follow a standard error response format:

```json
{
    "error": {
        "code": "ERROR_CODE",
        "message": "Error description",
        "details": [
            {
                "code": "DETAIL_ERROR_CODE",
                "message": "Detailed error description"
            }
        ]
    }
}
```

Common error codes:

| Error Code | Description |
|------------|-------------|
| AUTH_ERROR | Authentication error |
| INVALID_REQUEST | Invalid request parameters |
| OTP_EXPIRED | OTP has expired |
| INVALID_OTP | Incorrect OTP provided |
| ABHA_NOT_FOUND | ABHA ID not found |
| DUPLICATE_ABHA | ABHA ID already exists |
| SERVER_ERROR | Internal server error |

## Best Practices

1. **Error Handling**: Implement robust error handling for all API calls.
2. **Retry Mechanism**: Implement exponential backoff for retrying failed requests.
3. **Logging**: Maintain detailed logs of all API interactions for debugging.
4. **Security**: Store client credentials securely and never expose them in client-side code.
5. **Validation**: Validate all user inputs before making API calls.
6. **User Experience**: Provide clear feedback to users during the ABHA creation and verification process.

## Rate Limits

- Maximum 100 requests per minute per client ID
- Maximum 5 OTP generation requests per mobile number per day
- Maximum 3 failed OTP verification attempts before temporary lockout

## Health Records APIs

### 13. Fetch Health Records

Retrieves health records linked to an ABHA ID.

**Endpoint**: `GET /hiecm/gateway/v3/abha/health-records`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token
- X-TOKEN: Link token from previous API

**Query Parameters**:
- `fromDate`: Start date for records (format: YYYY-MM-DD)
- `toDate`: End date for records (format: YYYY-MM-DD)
- `recordType`: Type of records (e.g., "PRESCRIPTION", "LAB_REPORT", "DISCHARGE_SUMMARY")
- `page`: Page number for pagination
- `size`: Number of records per page

**Response**:
```json
{
    "healthRecords": [
        {
            "recordId": "**********",
            "recordType": "PRESCRIPTION",
            "facilityName": "City Hospital",
            "facilityId": "HFR123456",
            "doctorName": "Dr. Jane Smith",
            "doctorId": "HPR123456",
            "recordDate": "2023-05-15T10:30:00Z",
            "recordUrl": "https://example.com/records/**********",
            "recordStatus": "ACTIVE"
        }
    ],
    "totalRecords": 10,
    "totalPages": 1,
    "currentPage": 1
}
```

**Purpose**: Retrieves health records linked to an ABHA ID based on specified filters.

### 14. Download Health Record

Downloads a specific health record.

**Endpoint**: `GET /hiecm/gateway/v3/abha/health-records/{recordId}`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token
- X-TOKEN: Link token from previous API

**Response**: Binary file content with appropriate Content-Type header.

**Purpose**: Downloads a specific health record in its original format (PDF, FHIR, etc.).

### 14.1 Health Information Request

Initiates a request to fetch health information from healthcare providers.

**Endpoint**: `POST /hiecm/gateway/v3/health-information/request`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "requestId": "6f7c8421-9c10-4c84-8750-86d9c6548123",
    "consentId": "e7c8421-9c10-4c84-8750-86d9c6548789",
    "dateRange": {
        "from": "2023-01-01T00:00:00Z",
        "to": "2023-06-30T23:59:59Z"
    },
    "dataPushUrl": "https://example.com/data-push",
    "keyMaterial": {
        "cryptoAlg": "ECDH",
        "curve": "Curve25519",
        "dhPublicKey": {
            "expiry": "2023-12-31T23:59:59Z",
            "parameters": "base64-encoded-parameters",
            "keyValue": "base64-encoded-key"
        },
        "nonce": "base64-encoded-nonce"
    }
}
```

**Response**:
```json
{
    "requestId": "6f7c8421-9c10-4c84-8750-86d9c6548123",
    "transactionId": "7f7c8421-9c10-4c84-8750-86d9c6548456",
    "status": "PROCESSING"
}
```

**Purpose**: Initiates a request to fetch health information from healthcare providers based on a granted consent.

### 14.2 Health Information Status

Checks the status of a health information request.

**Endpoint**: `GET /hiecm/gateway/v3/health-information/status`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Query Parameters**:
- `requestId`: The ID of the health information request

**Response**:
```json
{
    "requestId": "6f7c8421-9c10-4c84-8750-86d9c6548123",
    "transactionId": "7f7c8421-9c10-4c84-8750-86d9c6548456",
    "status": "COMPLETED",
    "statusResponses": [
        {
            "hipId": "HIP123456",
            "status": "COMPLETED"
        }
    ]
}
```

**Purpose**: Checks the status of a health information request.

## Consent Management APIs

### 15. Request Consent

Requests consent from a patient to access their health records.

**Endpoint**: `POST /hiecm/gateway/v3/consent/request`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "requestId": "5f7c8421-9c10-4c84-8750-86d9c6548456",
    "abhaAddress": "johndoe@sbx",
    "purpose": {
        "code": "CAREMGT",
        "text": "Care Management"
    },
    "hiTypes": ["Prescription", "DiagnosticReport", "DischargeSummary"],
    "permission": {
        "accessMode": "VIEW",
        "dateRange": {
            "from": "2023-01-01T00:00:00Z",
            "to": "2023-12-31T23:59:59Z"
        },
        "frequency": {
            "unit": "HOUR",
            "value": 1,
            "repeats": 0
        },
        "dataEraseAt": "2024-01-01T00:00:00Z"
    }
}
```

**Response**:
```json
{
    "requestId": "5f7c8421-9c10-4c84-8750-86d9c6548456",
    "status": "REQUESTED",
    "createdAt": "2023-06-15T10:30:00Z",
    "expiresAt": "2023-06-15T11:30:00Z"
}
```

**Purpose**: Initiates a consent request to access a patient's health records.

### 16. Check Consent Status

Checks the status of a consent request.

**Endpoint**: `GET /hiecm/gateway/v3/consent/request/status`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Query Parameters**:
- `requestId`: The ID of the consent request

**Response**:
```json
{
    "requestId": "5f7c8421-9c10-4c84-8750-86d9c6548456",
    "status": "GRANTED",
    "consentId": "e7c8421-9c10-4c84-8750-86d9c6548789",
    "createdAt": "2023-06-15T10:30:00Z",
    "grantedAt": "2023-06-15T10:45:00Z",
    "expiresAt": "2023-06-15T11:30:00Z"
}
```

**Purpose**: Checks the current status of a consent request.

### 17. Fetch Consent Artifacts

Fetches the consent artifacts for a granted consent.

**Endpoint**: `GET /hiecm/gateway/v3/consent/fetch`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Query Parameters**:
- `consentId`: The ID of the granted consent

**Response**:
```json
{
    "consentId": "e7c8421-9c10-4c84-8750-86d9c6548789",
    "consentDetail": {
        "patient": {
            "id": "johndoe@sbx"
        },
        "careContexts": [
            {
                "patientReference": "123",
                "careContextReference": "456"
            }
        ],
        "purpose": {
            "code": "CAREMGT",
            "text": "Care Management"
        },
        "hiTypes": ["Prescription", "DiagnosticReport", "DischargeSummary"],
        "permission": {
            "accessMode": "VIEW",
            "dateRange": {
                "from": "2023-01-01T00:00:00Z",
                "to": "2023-12-31T23:59:59Z"
            },
            "frequency": {
                "unit": "HOUR",
                "value": 1,
                "repeats": 0
            },
            "dataEraseAt": "2024-01-01T00:00:00Z"
        }
    },
    "signature": "base64-encoded-signature"
}
```

**Purpose**: Fetches the detailed consent artifacts for a granted consent.

### 18. Revoke Consent

Revokes a previously granted consent.

**Endpoint**: `POST /hiecm/gateway/v3/consent/revoke`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "consentId": "e7c8421-9c10-4c84-8750-86d9c6548789",
    "reason": "NO_LONGER_NEEDED"
}
```

**Response**:
```json
{
    "status": "REVOKED",
    "revokedAt": "2023-06-15T11:00:00Z"
}
```

**Purpose**: Revokes a previously granted consent.

## Healthcare Provider Registry APIs

### 19. Search Healthcare Professionals

Searches for healthcare professionals in the registry.

**Endpoint**: `POST /hiecm/gateway/v3/healthcare-professional/search`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "name": "Dr. Smith",
    "speciality": "Cardiology",
    "location": "Bangalore",
    "languages": ["en", "hi"],
    "page": 1,
    "size": 10
}
```

**Response**:
```json
{
    "professionals": [
        {
            "hprId": "HPR123456",
            "name": "Dr. Jane Smith",
            "gender": "F",
            "speciality": "Cardiology",
            "qualifications": ["MBBS", "MD"],
            "languages": ["en", "hi"],
            "registrationDetails": {
                "council": "Medical Council of India",
                "registrationNumber": "MCI123456",
                "registrationYear": "2010"
            },
            "facilities": [
                {
                    "facilityId": "HFR123456",
                    "facilityName": "City Hospital",
                    "address": {
                        "line": "123 Main St",
                        "district": "Bangalore",
                        "state": "Karnataka",
                        "pincode": "560001"
                    }
                }
            ]
        }
    ],
    "totalCount": 1,
    "totalPages": 1,
    "currentPage": 1
}
```

**Purpose**: Searches for healthcare professionals based on specified criteria.

### 20. Get Healthcare Professional Details

Retrieves detailed information about a specific healthcare professional.

**Endpoint**: `GET /hiecm/gateway/v3/healthcare-professional/{hprId}`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Response**:
```json
{
    "hprId": "HPR123456",
    "name": "Dr. Jane Smith",
    "gender": "F",
    "dateOfBirth": "1980-01-01",
    "speciality": "Cardiology",
    "qualifications": ["MBBS", "MD"],
    "languages": ["en", "hi"],
    "registrationDetails": {
        "council": "Medical Council of India",
        "registrationNumber": "MCI123456",
        "registrationYear": "2010"
    },
    "facilities": [
        {
            "facilityId": "HFR123456",
            "facilityName": "City Hospital",
            "address": {
                "line": "123 Main St",
                "district": "Bangalore",
                "state": "Karnataka",
                "pincode": "560001"
            },
            "contactNumber": "9XXXXXXXXX",
            "email": "<EMAIL>"
        }
    ],
    "contactDetails": {
        "mobile": "9XXXXXXXXX",
        "email": "<EMAIL>"
    }
}
```

**Purpose**: Retrieves detailed information about a specific healthcare professional.

## Health Facility Registry APIs

### 21. Search Health Facilities

Searches for health facilities in the registry.

**Endpoint**: `POST /hiecm/gateway/v3/health-facility/search`

**Headers**:
- Content-Type: application/json
- REQUEST-ID: UUID
- TIMESTAMP: ISO 8601 timestamp
- X-CM-ID: sbx/abdm
- Authorization: Bearer token

**Request Body**:
```json
{
    "name": "City Hospital",
    "type": "Hospital",
    "location": "Bangalore",
    "services": ["Cardiology", "Neurology"],
    "page": 1,
    "size": 10
}
```

**Response**:
```json
{
    "facilities": [
        {
            "facilityId": "HFR123456",
            "facilityName": "City Hospital",
            "facilityType": "Hospital",
            "address": {
                "line": "123 Main St",
                "district": "Bangalore",
                "state": "Karnataka",
                "pincode": "560001"
            },
            "contactNumber": "9XXXXXXXXX",
            "email": "<EMAIL>",
            "services": ["Cardiology", "Neurology", "Orthopedics"]
        }
    ],
    "totalCount": 1,
    "totalPages": 1,
    "currentPage": 1
}
```

**Purpose**: Searches for health facilities based on specified criteria.

## Implementation Notes

1. Always use the latest version of the APIs (currently V3).
2. Test thoroughly in the sandbox environment before moving to production.
3. Ensure compliance with all ABDM security and privacy guidelines.
4. Keep client credentials confidential and never expose them in client-side code.
5. Implement proper error handling and user feedback mechanisms.
6. Follow the ABDM integration certification process before going live.
7. Implement proper logging for audit and debugging purposes.
8. Use appropriate security measures for storing sensitive data.
9. Implement proper session management and token refresh mechanisms.
10. Follow the ABDM UI/UX guidelines for a consistent user experience.
11. Implement proper encryption for sensitive data exchange.
12. Use appropriate key management practices for cryptographic operations.
13. Follow the FHIR standards for health information exchange where applicable.
14. Implement proper consent management workflows.
15. Ensure compliance with data retention policies.
