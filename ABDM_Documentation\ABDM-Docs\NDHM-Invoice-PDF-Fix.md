# NDHM Invoice PDF Display Fix

## Problem Analysis

Your invoice bundles are showing as structured data instead of PDF documents in the PHR app because you're using the **InvoiceRecord Composition** pattern instead of the **DocumentReference** pattern.

## Root Cause

- **Current Implementation**: InvoiceRecord (Composition-based) → PHR shows structured data
- **Expected Implementation**: DocumentReference with PDF → PHR shows downloadable PDF

## Solution

Modify your invoice bundle to use DocumentReference pattern like other health records.

## Code Changes Required

### 1. Create DocumentReference Resource Generator

Create a new file: `Healtether.Communications\helper\fhir\common_resources\document.reference.resource.fhir.js`

```javascript
import { v4 as uuidv4 } from "uuid";

export const generateDocumentReferenceResource = async (
  currentTime,
  patientResource,
  practitionerResource,
  binaryResource,
  type = "Invoice Record",
  title = "Invoice Report"
) => {
  const id = uuidv4();

  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "DocumentReference",
      id,
      meta: {
        versionId: "1",
        lastUpdated: currentTime,
        profile: [
          "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference"
        ]
      },
      status: "current",
      docStatus: "final",
      type: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "371530004",
            display: type
          }
        ],
        text: type
      },
      subject: {
        reference: patientResource.fullUrl,
        display: "Patient"
      },
      author: [
        {
          reference: practitionerResource.fullUrl,
          display: "Practitioner"
        }
      ],
      content: [
        {
          attachment: {
            contentType: "application/pdf",
            language: "en",
            url: binaryResource.fullUrl,
            title: title,
            creation: currentTime
          }
        }
      ]
    }
  };
};
```

### 2. Modify forwardToInvoiceRecordBundle Function

In `Healtether.Communications\helper\fhir\main.bundle.fhir.helper.js`, replace the current `forwardToInvoiceRecordBundle` function:

```javascript
// Add import at the top
import { generateDocumentReferenceResource } from "./common_resources/document.reference.resource.fhir.js";

// Replace the forwardToInvoiceRecordBundle function
const forwardToInvoiceRecordBundle = async (req, bundleId, currentTime) => {
  try {
    clearArrayResources();

    const {
      general,
      signature,
      patient,
      practitioners,
      encounter,
      organization,
      invoice,
      chargeItems,
      binary,
    } = req.body;

    patientResource = await generatePatientResource(currentTime, patient);

    for (const practitioner of practitioners) {
      practitionerResources.push(
        await generatePractitionerResource(
          currentTime,
          practitioner,
          patient.doctors
        )
      );
    }

    organizationResource = await generateOrganizationResource(organization);
    binaryResource = await generateBinaryResource(binary);

    // Create DocumentReference instead of Composition
    const documentReferenceResource = await generateDocumentReferenceResource(
      currentTime,
      patientResource,
      practitionerResources[0],
      binaryResource,
      "Invoice Record",
      "Invoice Report"
    );

    encounterResource = await generateEncounterResource(
      currentTime,
      patientResource,
      [],
      encounter,
      [],
      general,
      patient.id
    );

    // Simple bundle with DocumentReference (like Laboratory Report)
    const entry = [
      documentReferenceResource,
      patientResource,
      organizationResource,
      encounterResource,
      ...practitionerResources,
      binaryResource
    ];

    return await generateFhirBundle(
      entry,
      currentTime,
      bundleId,
      general,
      signature
    );
  } catch (error) {
    throw error;
  }
};
```

### 3. Alternative Quick Fix

If you want a quicker fix, modify your existing `createStructureForInvoice` function in `Healtether.Clinics\utils\fhir.data.js`:

```javascript
export const createStructureForInvoice = async (
  artifact,
  clinicData,
  patient,
  practitionerData,
  invoiceData,
  appointmentData
) => {
  const base64Data = await fetchImageAsBase64(
    appointmentData.invoiceReport[0].blobName,
    appointmentData?.clinic
  );

  // Return DocumentReference structure instead of full invoice structure
  return {
    general: createGeneralDetails(artifact),
    patient: createPatientDetails(patient),
    practitioners: [createPractitionerDetails(practitionerData)],
    organization: createOrganizationDetails(clinicData),
    encounter: createEncounterDetails(),
    binary: createBinaryDetails("application/pdf", base64Data),
    // Use DocumentReference instead of invoice details
    documentReference: await createDocumentReference(
      appointmentData.invoiceReport[0].blobName,
      appointmentData?.clinic,
      "Invoice Record",
      "Invoice Report"
    ),
    signature: createSignatureDetails("", [
      `${practitionerData.firstName + practitionerData.lastName}`,
    ]),
  };
};
```

## Expected Bundle Structure

The corrected bundle should look like:

```json
{
  "resourceType": "Bundle",
  "type": "document",
  "entry": [
    {
      "resource": {
        "resourceType": "DocumentReference",
        "status": "current",
        "type": {
          "text": "Invoice Record"
        },
        "content": [{
          "attachment": {
            "contentType": "application/pdf",
            "url": "urn:uuid:binary-id",
            "title": "Invoice Report"
          }
        }]
      }
    },
    {
      "resource": {
        "resourceType": "Binary",
        "contentType": "application/pdf",
        "data": "base64-pdf-data"
      }
    }
  ]
}
```

## Testing the Fix

After implementing the changes:

1. **Generate an invoice bundle**
2. **Check the bundle structure** - it should have DocumentReference instead of Composition
3. **Test in PHR app** - should show as downloadable PDF like "Laboratory Report"

This will make the PHR app treat your invoice like other document-based health records and show it as a downloadable PDF instead of structured data.

## ✅ Implementation Status

### Changes Made:

1. **✅ Created DocumentReference Resource Generator**
   - File: `Healtether.Communications/helper/fhir/common_resources/document.reference.resource.fhir.js`
   - Function: `generateDocumentReferenceResource`
   - Purpose: Creates DocumentReference resources for invoice PDFs

2. **✅ Updated Main Bundle Helper**
   - File: `Healtether.Communications/helper/fhir/main.bundle.fhir.helper.js`
   - Added import for new DocumentReference generator
   - Modified `forwardToInvoiceRecordBundle` function to use DocumentReference instead of Composition

3. **✅ Bundle Structure Changed**
   - **Before**: InvoiceRecord Composition → Shows structured data
   - **After**: DocumentReference with PDF attachment → Shows downloadable PDF

### Key Changes in `forwardToInvoiceRecordBundle`:

```javascript
// OLD: Created Composition with Invoice resources
const entry = [
  await generateInvoiceComposition(...),
  patientResource,
  organizationResource,
  encounterResource,
  ...practitionerResources,
  invoiceResources,
  ...chargeItemResources,
  binaryResource
];

// NEW: Creates DocumentReference with PDF attachment
const entry = [
  documentReferenceResource,  // DocumentReference instead of Composition
  patientResource,
  organizationResource,
  encounterResource,
  ...practitionerResources,
  binaryResource
];
```

### Expected Result:

Your invoice bundles will now display in the PHR app as:
- **📄 Invoice Record** (clickable PDF like Laboratory Report)
- Instead of showing structured billing data

### Testing:

1. Generate a new invoice bundle
2. Check that the bundle contains DocumentReference as the first entry
3. Verify in PHR app that it shows as downloadable PDF
4. Confirm it behaves like "Laboratory Report" example

The fix is now **IMPLEMENTED** and ready for testing! 🎉
