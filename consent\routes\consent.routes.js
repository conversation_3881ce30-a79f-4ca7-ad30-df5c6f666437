/**
 * Consent Routes
 *
 * This file defines the routes for consent management.
 * It includes routes for listing, viewing, approving, denying, and revoking consents.
 *
 * Official ABDM Documentation:
 * https://kiranma72.github.io/abdm-docs/5-building-a-phr-app/managing-consents/index.html
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Consent:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: MongoDB ID of the consent record
 *         userId:
 *           type: string
 *           description: ID of the user who granted this consent
 *         abhaAddress:
 *           type: string
 *           description: ABHA address associated with this consent
 *         consentId:
 *           type: string
 *           description: The consent ID from ABDM
 *         consentRequestId:
 *           type: string
 *           description: The consent request ID from ABDM
 *         status:
 *           type: string
 *           enum: [REQUESTED, GRANTED, DENIED, EXPIRED, REVOKED]
 *           description: The status of this consent
 *         purpose:
 *           type: string
 *           description: The purpose of this consent
 *         hiTypes:
 *           type: array
 *           items:
 *             type: string
 *           description: The health information types this consent is for
 *         permission:
 *           type: string
 *           enum: [VIEW, STORE]
 *           description: The permission for this consent
 *         dateRange:
 *           type: object
 *           properties:
 *             from:
 *               type: string
 *               format: date-time
 *               description: Start date for the consent
 *             to:
 *               type: string
 *               format: date-time
 *               description: End date for the consent
 *         frequency:
 *           type: object
 *           properties:
 *             unit:
 *               type: string
 *               enum: [HOUR, DAY, WEEK, MONTH, YEAR]
 *               description: Time unit for frequency
 *             value:
 *               type: integer
 *               description: Value for frequency
 *             repeats:
 *               type: integer
 *               description: Number of repeats
 *         requester:
 *           type: object
 *           properties:
 *             type:
 *               type: string
 *               enum: [HIU, HIP]
 *               description: Type of requester
 *             id:
 *               type: string
 *               description: ID of requester
 *             name:
 *               type: string
 *               description: Name of requester
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *         expiresAt:
 *           type: string
 *           format: date-time
 *           description: Expiration timestamp
 *   securitySchemes:
 *     authMiddleware:
 *       type: apiKey
 *       in: header
 *       name: Authorization
 *       description: Bearer token for authentication
 */

const express = require('express');
const router = express.Router();
const consentController = require('../controllers/consent.controller');
const authMiddleware = require('../../middlewares/auth.middleware');

// Protected routes (require authentication)

/**
 * @swagger
 * /api/consents:
 *   get:
 *     summary: List all consent requests for the authenticated user
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Maximum number of consents to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of consents to skip
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [REQUESTED, GRANTED, DENIED, EXPIRED, REVOKED]
 *         description: Filter consents by status
 *     responses:
 *       200:
 *         description: List of consent requests
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 consents:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Consent'
 *                 totalCount:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 offset:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authMiddleware, consentController.listConsentRequests);
 
/**
 * @swagger
 * /api/consents/{consentId}/revoke:
 *   post:
 *     summary: Revoke a consent
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: path
 *         name: consentId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the consent to revoke
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *             properties:
 *               reason:
 *                 type: string
 *                 description: Reason for revoking the consent
 *     responses:
 *       200:
 *         description: Consent revoked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 consentId:
 *                   type: string
 *       400:
 *         description: Bad request - reason is required or consent is not in GRANTED status
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Consent not found
 *       500:
 *         description: Server error
 */
router.post('/:consentId/revoke', authMiddleware, consentController.revokeConsent);

/**
 * @swagger
 * /api/consents/request/init:
 *   post:
 *     summary: Initialize a new consent request
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - purpose
 *             properties:
 *               patientId:
 *                 type: string
 *                 description: ABHA address of the patient
 *               purpose:
 *                 type: string
 *                 description: Purpose of the consent request
 *               purposeCode:
 *                 type: string
 *                 description: Code for the purpose
 *               hiTypes:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [DiagnosticReport, Prescription, DischargeSummary, OPConsultation, ImmunizationRecord, HealthDocumentRecord, WellnessRecord]
 *                 description: Types of health information to request
 *               accessMode:
 *                 type: string
 *                 enum: [VIEW, STORE]
 *                 default: VIEW
 *                 description: Mode of access for the consent
 *               dateRange:
 *                 type: object
 *                 properties:
 *                   from:
 *                     type: string
 *                     format: date-time
 *                     description: Start date for the consent
 *                   to:
 *                     type: string
 *                     format: date-time
 *                     description: End date for the consent
 *               dataEraseAt:
 *                 type: string
 *                 format: date-time
 *                 description: Date when data should be erased
 *               frequency:
 *                 type: object
 *                 properties:
 *                   unit:
 *                     type: string
 *                     enum: [HOUR, DAY, WEEK, MONTH, YEAR]
 *                     default: HOUR
 *                   value:
 *                     type: integer
 *                     default: 1
 *                   repeats:
 *                     type: integer
 *                     default: 0
 *               requesterName:
 *                 type: string
 *                 description: Name of the requester
 *               requesterIdentifier:
 *                 type: string
 *                 description: Identifier of the requester
 *     responses:
 *       200:
 *         description: Consent request initialized successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - missing required fields
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post('/request/init', authMiddleware, consentController.initializeConsentRequest);

/**
 * @swagger
 * /api/consents/request/{requestId}/approve:
 *   post:
 *     summary: Approve a consent request
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: path
 *         name: requestId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the consent request to approve
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - consentIds
 *             properties:
 *               consentIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of consent IDs to approve
 *     responses:
 *       200:
 *         description: Consent request approved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 requestId:
 *                   type: string
 *                 transactionId:
 *                   type: string
 *       400:
 *         description: Bad request - missing required fields
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Consent request not found
 *       500:
 *         description: Server error
 */
router.post('/request/:requestId/approve', authMiddleware, consentController.approveConsentRequest);

/**
 * @swagger
 * /api/consents/request/{requestId}/deny:
 *   post:
 *     summary: Deny a consent request
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: path
 *         name: requestId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the consent request to deny
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *             properties:
 *               reason:
 *                 type: string
 *                 description: Reason for denying the consent request
 *     responses:
 *       200:
 *         description: Consent request denied successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 requestId:
 *                   type: string
 *                 transactionId:
 *                   type: string
 *       400:
 *         description: Bad request - missing required fields or consent not in REQUESTED status
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Consent request not found
 *       500:
 *         description: Server error
 */
router.post('/request/:requestId/deny', authMiddleware, consentController.denyConsentRequest);

/**
 * @swagger
 * /api/consents/{consentId}/fetch:
 *   post:
 *     summary: Fetch consent details from ABDM
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: path
 *         name: consentId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the consent to fetch
 *     responses:
 *       200:
 *         description: Consent fetch initiated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 consentId:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Consent not found
 *       500:
 *         description: Server error
 */
router.post('/:consentId/fetch', authMiddleware, consentController.getConsentDetails);
 
// PIN management routes
/**
 * @swagger
 * /api/consents/pin/create:
 *   post:
 *     summary: Create a consent PIN
 *     tags: [Consent PIN]
 *     security:
 *       - authMiddleware: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - pin
 *             properties:
 *               pin:
 *                 type: string
 *                 minLength: 4
 *                 maxLength: 6
 *                 description: PIN for consent operations
 *     responses:
 *       200:
 *         description: Consent PIN created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - invalid PIN format
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/pin/create', authMiddleware, consentController.createConsentPin);

/**
 * @swagger
 * /api/consents/pin/verify:
 *   post:
 *     summary: Verify a consent PIN
 *     tags: [Consent PIN]
 *     security:
 *       - authMiddleware: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - pin
 *             properties:
 *               pin:
 *                 type: string
 *                 description: PIN to verify
 *     responses:
 *       200:
 *         description: PIN verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 verified:
 *                   type: boolean
 *       400:
 *         description: Bad request - invalid PIN
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/pin/verify', authMiddleware, consentController.verifyConsentPin);

/**
 * @swagger
 * /api/consents/pin/change:
 *   post:
 *     summary: Change a consent PIN
 *     tags: [Consent PIN]
 *     security:
 *       - authMiddleware: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - oldPin
 *               - newPin
 *             properties:
 *               oldPin:
 *                 type: string
 *                 description: Current PIN
 *               newPin:
 *                 type: string
 *                 minLength: 4
 *                 maxLength: 6
 *                 description: New PIN
 *     responses:
 *       200:
 *         description: PIN changed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - invalid PIN format or incorrect old PIN
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/pin/change', authMiddleware, consentController.changeConsentPin);

/**
 * @swagger
 * /api/consents/pin/forgot/generate-otp:
 *   post:
 *     summary: Generate OTP for forgot PIN flow
 *     tags: [Consent PIN]
 *     security:
 *       - authMiddleware: []
 *     responses:
 *       200:
 *         description: OTP generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 transactionId:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/pin/forgot/generate-otp', authMiddleware, consentController.generateForgotPinOtp);

/**
 * @swagger
 * /api/consents/pin/forgot/validate-otp:
 *   post:
 *     summary: Validate OTP for forgot PIN flow
 *     tags: [Consent PIN]
 *     security:
 *       - authMiddleware: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - otp
 *               - transactionId
 *             properties:
 *               otp:
 *                 type: string
 *                 description: OTP received
 *               transactionId:
 *                 type: string
 *                 description: Transaction ID from generate-otp call
 *     responses:
 *       200:
 *         description: OTP validated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 validated:
 *                   type: boolean
 *       400:
 *         description: Bad request - invalid OTP or transaction ID
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/pin/forgot/validate-otp', authMiddleware, consentController.validateForgotPinOtp);

/**
 * @swagger
 * /api/consents/pin/reset:
 *   post:
 *     summary: Reset consent PIN after OTP validation
 *     tags: [Consent PIN]
 *     security:
 *       - authMiddleware: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - pin
 *               - transactionId
 *             properties:
 *               pin:
 *                 type: string
 *                 minLength: 4
 *                 maxLength: 6
 *                 description: New PIN
 *               transactionId:
 *                 type: string
 *                 description: Transaction ID from validate-otp call
 *     responses:
 *       200:
 *         description: PIN reset successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - invalid PIN format or transaction ID
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/pin/reset', authMiddleware, consentController.resetConsentPin);

/**
 * @swagger
 * /api/consents/pin/status:
 *   get:
 *     summary: Check consent PIN status for current profile
 *     tags: [Consent PIN]
 *     security:
 *       - authMiddleware: []
 *     responses:
 *       200:
 *         description: PIN status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                     hasPinSet:
 *                       type: boolean
 *                     isLocked:
 *                       type: boolean
 *                     failedAttempts:
 *                       type: integer
 *                     lockedUntil:
 *                       type: string
 *                       format: date-time
 *                       nullable: true
 *                     profileAbhaAddress:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/pin/status', authMiddleware, consentController.checkConsentPinStatus);

/**
 * @swagger
 * /api/consents/auto-approve:
 *   post:
 *     summary: Set up auto-approve consent for the authenticated user
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     responses:
 *       200:
 *         description: Auto-approve consent set up successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Server error
 */
router.post('/auto-approve', authMiddleware, consentController.consentAutoApprove);

/**
 * @swagger
 * /api/consents/{consentId}/auto-approve:
 *   post:
 *     summary: Toggle auto-approve for a specific consent
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: path
 *         name: consentId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the consent to toggle auto-approve for
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - enable
 *             properties:
 *               enable:
 *                 type: boolean
 *                 description: Whether to enable or disable auto-approve
 *     responses:
 *       200:
 *         description: Auto-approve toggled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       400:
 *         description: Bad request - enable flag is required
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Consent not found
 *       500:
 *         description: Server error
 */
router.post('/:consentId/auto-approve', authMiddleware, consentController.toggleConsentAutoApprove);

/**
 * @swagger
 * /api/consents/request/{requestId}/details:
 *   get:
 *     summary: Get detailed information about a specific consent request
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: path
 *         name: requestId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the consent request to retrieve details for
 *     responses:
 *       200:
 *         description: Consent request details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       400:
 *         description: Bad request - requestId is required
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Consent request not found
 *       500:
 *         description: Server error
 */
router.get('/request/:requestId/details', authMiddleware, consentController.getConsentRequestDetails);

/**
 * @swagger
 * /api/consents/request/{requestId}/artifacts:
 *   get:
 *     summary: Get all consent artifacts associated with a specific request
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: path
 *         name: requestId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the consent request to retrieve artifacts for
 *     responses:
 *       200:
 *         description: Consent artifacts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *       400:
 *         description: Bad request - requestId is required
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Consent request not found
 *       500:
 *         description: Server error
 */
router.get('/request/:requestId/artifacts', authMiddleware, consentController.getConsentArtifactsByRequestId);

/**
 * @swagger
 * /api/consents/artifact/{artifactId}:
 *   get:
 *     summary: Get detailed information about a specific consent artifact
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: path
 *         name: artifactId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the consent artifact to retrieve details for
 *     responses:
 *       200:
 *         description: Consent artifact details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *       400:
 *         description: Bad request - artifactId is required
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Consent artifact not found
 *       500:
 *         description: Server error
 */
router.get('/artifact/:artifactId', authMiddleware, consentController.getConsentArtifactById);

/**
 * @swagger
 * /api/consents/artifacts:
 *   get:
 *     summary: Get all consent artifacts for the user's ABHA address
 *     tags: [Consents]
 *     security:
 *       - authMiddleware: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Maximum number of artifacts to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of artifacts to skip
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [REQUESTED, GRANTED, DENIED, EXPIRED, REVOKED]
 *         description: Filter artifacts by status
 *     responses:
 *       200:
 *         description: Consent artifacts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     size:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     offset:
 *                       type: integer
 *                     consentArtefacts:
 *                       type: array
 *                       items:
 *                         type: object
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/artifacts', authMiddleware, consentController.getConsentArtifactsByAbhaAddress);

// Note: Callback routes have been moved to direct routes in index.js
// The Swagger documentation is kept here for reference

/**
 * @swagger
 * /api/v3/hiu/consent/notification:
 *   post:
 *     summary: Handle consent notification callback from ABDM
 *     tags: [Consent Callbacks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notification:
 *                 type: object
 *                 properties:
 *                   consentRequestId:
 *                     type: string
 *                   status:
 *                     type: string
 *                     enum: [GRANTED, DENIED, EXPIRED, REVOKED]
 *                   reason:
 *                     type: string
 *                     nullable: true
 *                   consentArtefacts:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *     responses:
 *       202:
 *         description: Notification acknowledged
 */

/**
 * @swagger
 * /api/v3/hiu/consent/on-init:
 *   post:
 *     summary: Handle consent request initialization callback from ABDM
 *     tags: [Consent Callbacks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               consentRequest:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *               error:
 *                 type: object
 *                 nullable: true
 *               response:
 *                 type: object
 *                 properties:
 *                   requestId:
 *                     type: string
 *     responses:
 *       202:
 *         description: Callback acknowledged
 */

/**
 * @swagger
 * /api/v3/hiu/consent/on-fetch:
 *   post:
 *     summary: Handle consent fetch callback from ABDM
 *     tags: [Consent Callbacks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               consent:
 *                 type: object
 *                 properties:
 *                   consentId:
 *                     type: string
 *                   consentDetail:
 *                     type: object
 *               error:
 *                 type: object
 *                 nullable: true
 *     responses:
 *       202:
 *         description: Callback acknowledged
 */

/**
 * @swagger
 * /api/v3/hiu/consent/on-status:
 *   post:
 *     summary: Handle consent request status callback from ABDM
 *     tags: [Consent Callbacks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               consentRequest:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   status:
 *                     type: string
 *               error:
 *                 type: object
 *                 nullable: true
 *               response:
 *                 type: object
 *     responses:
 *       202:
 *         description: Callback acknowledged
 */

/**
 * @swagger
 * /api/v3/hiu/consent/request/notify:
 *   post:
 *     summary: Handle consent request notification from HIE-CM to HIU when a consent is approved/revoked/denied
 *     tags: [Consent Callbacks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [GRANTED, DENIED, REVOKED]
 *                 description: The status of the consent request
 *               consentRequestId:
 *                 type: string
 *                 description: The ID of the consent request
 *               reason:
 *                 type: string
 *                 nullable: true
 *                 description: Reason for denying or revoking the consent (only for DENIED or REVOKED status)
 *               consentArtefacts:
 *                 type: array
 *                 description: List of consent artefact IDs (only for GRANTED status)
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: The ID of the consent artefact
 *     responses:
 *       202:
 *         description: Notification acknowledged
 */

module.exports = router;
